# Gmail to Calendar Follow-up Script

This Google Apps Script automatically monitors your Gmail inbox for emails from specific senders and creates calendar events for follow-up reminders.

## Features

- ✅ **Monitors specific email addresses** - Only processes emails from your defined list
- ✅ **Prevents duplicate processing** - Uses Gmail labels to track processed emails
- ✅ **Automatic calendar events** - Creates 30-minute follow-up events
- ✅ **Smart reminders** - 10-minute popup reminders before events
- ✅ **Cloud-based** - Runs automatically even when your computer is off
- ✅ **Idempotent** - Safe to run multiple times without creating duplicates
- ✅ **Detailed logging** - Comprehensive error handling and logging

## Setup Instructions

### Step 1: Create the Google Apps Script

1. Go to [script.google.com](https://script.google.com)
2. Click "New Project"
3. Delete the default code and paste the contents of `gmail-calendar-monitor.gs`
4. Save the project with a meaningful name (e.g., "Gmail Follow-up Monitor")

### Step 2: Configure Email Addresses

Update the `MONITORED_SENDERS` array in the script with your specific email addresses:

```javascript
const MONITORED_SENDERS = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
];
```

### Step 3: Initial Setup

1. In the Google Apps Script editor, select the `setupScript` function from the dropdown
2. Click the "Run" button
3. Grant the necessary permissions when prompted:
   - Gmail access (to read emails and create labels)
   - Calendar access (to create events)
4. Check the execution log to confirm setup was successful

### Step 4: Create Automatic Trigger

1. In the script editor, select the `createTimeDrivenTrigger` function
2. Click "Run" to create the automatic trigger
3. The script will now run every 10 minutes automatically

## How It Works

### Email Processing Flow

1. **Search for new emails** - Finds emails from monitored senders without the "FollowUp" label
2. **Apply label** - Adds "FollowUp" label to prevent reprocessing
3. **Create calendar event** - Generates a 30-minute follow-up event
4. **Set reminder** - Adds a 10-minute popup reminder

### Calendar Event Details

- **Title**: "Follow-up: [Original Email Subject]"
- **Duration**: 30 minutes (starting from current time)
- **Description**: Includes sender, subject, timestamp, and email preview
- **Reminder**: 10-minute popup notification

### Gmail Label System

The script creates and uses a "FollowUp" label to track processed emails:
- Prevents duplicate calendar events
- Allows manual review of processed emails
- Can be customized by changing the `FOLLOW_UP_LABEL` constant

## Customization Options

### Timing Settings

```javascript
// Change event duration (default: 30 minutes)
const EVENT_DURATION_MINUTES = 45;

// Change reminder time (default: 10 minutes before)
const REMINDER_MINUTES = 15;
```

### Processing Frequency

To change how often the script runs, modify the trigger:

```javascript
// Run every 5 minutes instead of 10
ScriptApp.newTrigger('processNewEmails')
  .timeBased()
  .everyMinutes(5)
  .create();
```

### Email Search Criteria

You can modify the search query to add additional filters:

```javascript
// Example: Only process emails with "urgent" in subject
const searchQuery = `from:${senderEmail} -label:${FOLLOW_UP_LABEL} subject:urgent`;
```

## Testing

### Manual Testing

1. Select the `testProcessing` function and click "Run"
2. Check the execution log for processing details
3. Verify that calendar events are created correctly

### Debugging

- Check the "Executions" tab in Google Apps Script for detailed logs
- Look for any error messages in the console
- Verify that the "FollowUp" label appears in Gmail

## Troubleshooting

### Common Issues

**Script not running automatically:**
- Verify the trigger was created successfully
- Check the "Triggers" tab in Google Apps Script
- Ensure you have the necessary permissions

**No calendar events created:**
- Confirm you have calendar access permissions
- Check that the default calendar is accessible
- Verify email addresses in MONITORED_SENDERS are correct

**Duplicate events:**
- The "FollowUp" label should prevent this
- Check if the label is being applied correctly
- Verify the search query is working as expected

### Permission Requirements

The script requires the following Google permissions:
- **Gmail**: Read emails, create and manage labels
- **Calendar**: Create events and set reminders
- **Script**: Run time-driven triggers

## Security Notes

- The script only reads emails from specified senders
- No email content is stored permanently
- All processing happens within Google's secure environment
- You can revoke permissions at any time through Google Account settings

## Support

For issues or questions:
1. Check the execution logs in Google Apps Script
2. Review the troubleshooting section above
3. Test with the manual `testProcessing` function
4. Verify your Gmail search syntax is correct

## Version History

- **v1.0**: Initial release with core functionality
  - Email monitoring from specific senders
  - Automatic calendar event creation
  - Duplicate prevention with labels
  - Time-driven triggers
