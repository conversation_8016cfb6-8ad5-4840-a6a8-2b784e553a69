/**
 * Gmail to Calendar Follow-up Script
 * 
 * This Google Apps Script monitors Gmail for emails from specific senders
 * and automatically creates calendar events for follow-up reminders.
 * 
 * Setup Instructions:
 * 1. Open script.google.com
 * 2. Create a new project and paste this code
 * 3. Update the MONITORED_SENDERS array with your specific email addresses
 * 4. Run setupScript() once to create the label and set permissions
 * 5. Set up a time-driven trigger to run processNewEmails() every 10 minutes
 * 
 * Author: Generated for Email Sort Calendar Project
 * Version: 1.0
 */

// Configuration - Update these email addresses as needed
const MONITORED_SENDERS = [
  '<EMAIL>',
  '<EMAIL>'
];

// Gmail label name to track processed emails
const FOLLOW_UP_LABEL = 'FollowUp';

// Calendar event duration in minutes
const EVENT_DURATION_MINUTES = 30;

// Reminder time in minutes before the event
const REMINDER_MINUTES = 10;

/**
 * Main function to process new emails from monitored senders
 * This function should be called by the time-driven trigger
 */
function processNewEmails() {
  try {
    console.log('Starting email processing...');
    
    // Get or create the FollowUp label
    const followUpLabel = getOrCreateLabel(FOLLOW_UP_LABEL);
    
    // Process emails from each monitored sender
    MONITORED_SENDERS.forEach(senderEmail => {
      processEmailsFromSender(senderEmail, followUpLabel);
    });
    
    console.log('Email processing completed successfully');
    
  } catch (error) {
    console.error('Error in processNewEmails:', error);
    // Optionally send an email notification about the error
    // GmailApp.sendEmail('<EMAIL>', 'Script Error', error.toString());
  }
}

/**
 * Process emails from a specific sender
 * @param {string} senderEmail - The email address to monitor
 * @param {GmailLabel} followUpLabel - The Gmail label object
 */
function processEmailsFromSender(senderEmail, followUpLabel) {
  console.log(`Processing emails from: ${senderEmail}`);
  
  // Search for emails from this sender that don't have the FollowUp label
  // Using Gmail search syntax to find unprocessed emails
  const searchQuery = `from:${senderEmail} -label:${FOLLOW_UP_LABEL}`;
  
  // Get email threads matching the criteria
  const threads = GmailApp.search(searchQuery, 0, 50); // Limit to 50 most recent
  
  console.log(`Found ${threads.length} unprocessed threads from ${senderEmail}`);
  
  // Process each thread
  threads.forEach(thread => {
    try {
      processEmailThread(thread, followUpLabel);
    } catch (error) {
      console.error(`Error processing thread ${thread.getId()}:`, error);
    }
  });
}

/**
 * Process a single email thread
 * @param {GmailThread} thread - The Gmail thread to process
 * @param {GmailLabel} followUpLabel - The Gmail label object
 */
function processEmailThread(thread, followUpLabel) {
  // Get the first (original) message in the thread
  const messages = thread.getMessages();
  const firstMessage = messages[0];
  
  // Extract email details
  const sender = firstMessage.getFrom();
  const subject = firstMessage.getSubject();
  const receivedTime = firstMessage.getDate();
  const body = firstMessage.getPlainBody();
  
  console.log(`Processing email: "${subject}" from ${sender}`);
  
  // Apply the FollowUp label to prevent reprocessing
  thread.addLabel(followUpLabel);
  
  // Create calendar event
  createFollowUpEvent(sender, subject, receivedTime, body);
  
  console.log(`Successfully processed and labeled email: "${subject}"`);
}

/**
 * Create a follow-up calendar event
 * @param {string} sender - Email sender
 * @param {string} subject - Email subject
 * @param {Date} receivedTime - When the email was received
 * @param {string} body - Email body content (first 500 chars)
 */
function createFollowUpEvent(sender, subject, receivedTime, body) {
  // Get the default calendar
  const calendar = CalendarApp.getDefaultCalendar();
  
  // Set event start time to current time
  const startTime = new Date();
  
  // Set event end time (30 minutes later)
  const endTime = new Date(startTime.getTime() + (EVENT_DURATION_MINUTES * 60 * 1000));
  
  // Create event title
  const eventTitle = `Follow-up: ${subject}`;
  
  // Create detailed description
  const description = createEventDescription(sender, subject, receivedTime, body);
  
  // Create the calendar event
  const event = calendar.createEvent(
    eventTitle,
    startTime,
    endTime,
    {
      description: description,
      location: '', // Can be customized if needed
    }
  );
  
  // Add popup reminder (10 minutes before)
  event.addPopupReminder(REMINDER_MINUTES);
  
  console.log(`Created calendar event: "${eventTitle}" at ${startTime}`);
}

/**
 * Create a detailed event description
 * @param {string} sender - Email sender
 * @param {string} subject - Email subject
 * @param {Date} receivedTime - When the email was received
 * @param {string} body - Email body content
 * @return {string} Formatted description
 */
function createEventDescription(sender, subject, receivedTime, body) {
  // Truncate body to prevent overly long descriptions
  const truncatedBody = body.length > 500 ? body.substring(0, 500) + '...' : body;
  
  return `FOLLOW-UP REMINDER
  
📧 From: ${sender}
📋 Subject: ${subject}
📅 Received: ${receivedTime.toLocaleString()}

📝 Email Preview:
${truncatedBody}

---
This event was automatically created by Gmail Follow-up Script`;
}

/**
 * Get existing label or create a new one
 * @param {string} labelName - Name of the label
 * @return {GmailLabel} The Gmail label object
 */
function getOrCreateLabel(labelName) {
  // Try to get existing label
  let label = GmailApp.getUserLabelByName(labelName);
  
  // Create label if it doesn't exist
  if (!label) {
    console.log(`Creating new Gmail label: ${labelName}`);
    label = GmailApp.createLabel(labelName);
  }
  
  return label;
}

/**
 * Setup function - Run this once manually to initialize the script
 * This function creates the necessary label and can be used to test permissions
 */
function setupScript() {
  console.log('Setting up Gmail Follow-up Script...');
  
  try {
    // Create the FollowUp label if it doesn't exist
    const label = getOrCreateLabel(FOLLOW_UP_LABEL);
    console.log(`✅ Label "${FOLLOW_UP_LABEL}" is ready`);
    
    // Test Gmail access
    const testThreads = GmailApp.search('in:inbox', 0, 1);
    console.log('✅ Gmail access confirmed');
    
    // Test Calendar access
    const calendar = CalendarApp.getDefaultCalendar();
    console.log(`✅ Calendar access confirmed: ${calendar.getName()}`);
    
    console.log('🎉 Setup completed successfully!');
    console.log('Next steps:');
    console.log('1. Update MONITORED_SENDERS array with your email addresses');
    console.log('2. Set up a time-driven trigger for processNewEmails()');
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
    console.log('Please ensure you have granted necessary permissions');
  }
}

/**
 * Function to create a time-driven trigger
 * Run this once to set up automatic execution every 10 minutes
 */
function createTimeDrivenTrigger() {
  // Delete existing triggers for this function to avoid duplicates
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => {
    if (trigger.getHandlerFunction() === 'processNewEmails') {
      ScriptApp.deleteTrigger(trigger);
    }
  });
  
  // Create new trigger to run every 10 minutes
  ScriptApp.newTrigger('processNewEmails')
    .timeBased()
    .everyMinutes(10)
    .create();
    
  console.log('✅ Time-driven trigger created - script will run every 10 minutes');
}

/**
 * Test function to manually process a small batch of emails
 * Useful for testing without waiting for the trigger
 */
function testProcessing() {
  console.log('Running test processing...');
  processNewEmails();
}
